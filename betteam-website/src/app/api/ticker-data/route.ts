import { NextResponse } from 'next/server';

const tickerData = [
  { id: '1', match: 'Real Madrid vs Barcelona', result: '2-1', odds: '1.85' },
  { id: '2', match: 'Manchester United vs Liverpool', result: '1-0', odds: '2.10' },
  { id: '3', match: 'Bayern Munich vs Dortmund', result: '3-2', odds: '1.95' },
  { id: '4', match: 'PSG vs Marseille', result: '2-0', odds: '1.75' },
  { id: '5', match: 'Juventus vs AC Milan', result: '1-1', odds: '2.25' },
  { id: '6', match: 'Chelsea vs Arsenal', result: '0-2', odds: '2.40' },
  { id: '7', match: 'Atletico Madrid vs Valencia', result: '3-1', odds: '1.65' },
  { id: '8', match: 'Inter Milan vs Napoli', result: '2-2', odds: '2.15' },
  { id: '9', match: 'Sevilla vs Real Betis', result: '1-0', odds: '1.90' },
  { id: '10', match: 'Tottenham vs West Ham', result: '2-1', odds: '2.05' }
];

export async function GET() {
  return NextResponse.json(tickerData);
}
