import Link from 'next/link';
import { Trophy, Home } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-primary flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="flex justify-center mb-6">
          <Trophy className="w-16 h-16 text-yellow-500" />
        </div>
        
        <h1 className="text-6xl font-bold text-white mb-4">404</h1>
        
        <h2 className="text-2xl font-semibold text-white mb-4">
          Página no encontrada
        </h2>
        
        <p className="text-gray-400 mb-8">
          Lo sentimos, la página que buscas no existe o ha sido movida.
        </p>
        
        <Link
          href="/es-MX"
          className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors min-h-[44px]"
        >
          <Home className="w-5 h-5" />
          <span>Volver al inicio</span>
        </Link>
      </div>
    </div>
  );
}
