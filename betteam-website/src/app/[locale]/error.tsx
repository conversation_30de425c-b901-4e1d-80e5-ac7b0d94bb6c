'use client';

import { useEffect } from 'react';
import { Trophy } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen bg-primary flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="flex justify-center mb-6">
          <Trophy className="w-16 h-16 text-red-500" />
        </div>
        
        <h1 className="text-2xl font-bold text-white mb-4">
          ¡Algo salió mal!
        </h1>
        
        <p className="text-gray-400 mb-8">
          Lo sentimos, ha ocurrido un error inesperado. Por favor, inténtalo de nuevo.
        </p>
        
        <button
          onClick={reset}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors min-h-[44px] min-w-[44px]"
        >
          Intentar de nuevo
        </button>
      </div>
    </div>
  );
}
