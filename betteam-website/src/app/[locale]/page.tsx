import { getTranslations } from 'next-intl/server';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import Ticker from '@/components/Ticker';
import StructuredData from '@/components/StructuredData';
import {
  SlideUpSection,
  SlideInLeftSection,
  SlideInRightSection,
  ZoomInSection,
  BounceInSection,
  FadeInSection
} from '@/components/ScrollAnimationWrapper';
import { StaggeredStatsGrid, StaggeredFAQList } from '@/components/StaggeredGrid';
import { SimpleFAQSection } from '@/components/AnimatedFAQ';
import {
  LazyTestimonialCarousel,
  LazyCertificateCarousel,
  LazyListadoSection,
  LazyPayChannelSection
} from '@/components/LazyComponents';
import AnimatedFooter from '@/components/AnimatedFooter';

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  // 从多语言配置中获取 testimonials 数据
  const testimonialsData = t.raw('testimonials.items') as Array<{
    name: string;
    rating: number;
    comment: string;
  }>;
  
  const testimonials = testimonialsData.map((item, index) => ({
    id: (index + 1).toString(),
    type: 'testimonial' as const,
    name: item.name,
    rating: item.rating,
    comment: item.comment
  }));

  // Sample data for certificates
  const certificates = [
    {
      id: '1',
      image: '/certificates/placeholder.svg'
    },
    {
      id: '2',
      image: '/certificates/placeholder.svg'
    },
    {
      id: '3',
      image: '/certificates/placeholder.svg'
    }
  ];

  return (
    <div className="min-h-screen bg-primary">
      <StructuredData locale={locale} />
      
      {/* Header 和 Hero Section 的背景容器 */}
      <div 
        className="relative bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/hero/hero_bg.png')"
        }}
      >
        {/* Header */}
        <Header
          translations={{
            listado: t('header.nav.listado'),
            comunidad: t('header.nav.comunidad'),
            certificados: t('header.nav.certificados'),
            faq: t('header.nav.faq')
          }}
        />

        {/* Hero Section */}
        <HeroSection 
          translations={{
            title: t('hero.title'),
            badge: t('hero.badge'),
            description: t('hero.description'),
            cta: {
              primary: t('hero.cta.primary'),
              secondary: t('hero.cta.secondary')
            }
          }}
        />
      </div>

      {/* Stats Section */}
      <section className="pt-8 pb-8 bg-primary">
        <div className="container mx-auto px-4">
          <StaggeredStatsGrid className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
            {/* +12 canales oficiales */}
            <div className="stats-card">
              <div className="stats-value">
                {t('stats.channels.value')}
              </div>
              <div className="stats-label">
                {t('stats.channels.label')}
              </div>
            </div>

            {/* 24/7 soporte en español */}
            <div className="stats-card">
              <div className="stats-value">
                {t('stats.support.value')}
              </div>
              <div className="stats-label">
                {t('stats.support.label')}
              </div>
            </div>

            {/* MXN depósitos & retiros */}
            <div className="stats-card">
              <div className="stats-value">
                {t('stats.deposits.value')}
              </div>
              <div className="stats-label">
                {t('stats.deposits.label')}
              </div>
            </div>

            {/* 256-BIT cifrado SSL */}
            <div className="stats-card">
              <div className="stats-value">
                {t('stats.security.value')}
              </div>
              <div className="stats-label">
                {t('stats.security.label')}
              </div>
            </div>
          </StaggeredStatsGrid>
        </div>
      </section>

      {/* Ticker */}
      <Ticker />

      {/* Listado Section - 平台和渠道验证 */}
      <SlideInRightSection>
        <LazyListadoSection />
      </SlideInRightSection>

      {/* Community/Testimonials Section */}
      <section id="comunidad" className="pt-8 pb-8 bg-primary">
        <div className="container mx-auto px-4">
          <SlideUpSection>
            <h2
              className="text-white mb-16"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 900,
                fontStyle: 'normal',
                fontSize: '30px',
                lineHeight: '100%',
                letterSpacing: '0%',
                textAlign: 'left'
              }}
            >
              {t('testimonials.title')}
            </h2>
          </SlideUpSection>

          <SlideInRightSection delay={600}>
            <LazyTestimonialCarousel items={testimonials} />
          </SlideInRightSection>
        </div>
      </section>

      {/* Certificates Section */}
      <section id="certificados" className="pt-8 pb-8 bg-primary">
        <div className="container mx-auto px-4">
          <BounceInSection>
            <h2
              className="text-white mb-16"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 900,
                fontStyle: 'normal',
                fontSize: '30px',
                lineHeight: '100%',
                letterSpacing: '0%',
                textAlign: 'left'
              }}
            >
              {t('certificates.title')}
            </h2>
          </BounceInSection>

          <ZoomInSection delay={800}>
            <LazyCertificateCarousel items={certificates} />
          </ZoomInSection>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="pt-8 pb-8 bg-primary">
        <div className="container mx-auto px-4">
          <BounceInSection>
            <h2
              className="text-white mb-16"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 900,
                fontStyle: 'normal',
                fontSize: '30px',
                lineHeight: '100%',
                letterSpacing: '0%',
                textAlign: 'left'
              }}
            >
              {t('faq.title')}
            </h2>
          </BounceInSection>

          <StaggeredFAQList className="space-y-4">
            {[0, 1, 2, 3].map((index) => (
              <SimpleFAQSection
                key={index}
                question={t(`faq.items.${index}.question`)}
                answer={t(`faq.items.${index}.answer`)}
                index={index}
              />
            ))}
          </StaggeredFAQList>
        </div>
      </section>

      {/* Pay Channel Section */}
      <SlideUpSection>
        <LazyPayChannelSection />
      </SlideUpSection>

      {/* Footer */}
      <AnimatedFooter />
    </div>
  );
}
