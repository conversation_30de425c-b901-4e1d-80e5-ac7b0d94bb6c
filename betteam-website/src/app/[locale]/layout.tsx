import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Montserrat } from 'next/font/google';
import { Metadata } from 'next';
import '../globals.css';

const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  preload: true
});

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  const isSpanish = locale === 'es-MX';

  return {
    title: isSpanish
      ? 'BetTeam - Tu Equipo Para Apostar Con Confianza'
      : 'BetTeam - Your Team To Bet With Confidence',
    description: isSpanish
      ? 'Únete a miles de apostadores que confían en nuestros pronósticos expertos y análisis detallados para maximizar sus ganancias.'
      : 'Join thousands of bettors who trust our expert predictions and detailed analysis to maximize their winnings.',
    keywords: isSpanish
      ? 'apuestas deportivas, pronósticos, análisis, fútbol, deportes, ganancias'
      : 'sports betting, predictions, analysis, football, sports, winnings',
    authors: [{ name: 'BetTeam' }],
    creator: 'BetTeam',
    publisher: 'BetTeam',
    robots: 'index, follow',
    openGraph: {
      title: isSpanish
        ? 'BetTeam - Tu Equipo Para Apostar Con Confianza'
        : 'BetTeam - Your Team To Bet With Confidence',
      description: isSpanish
        ? 'Únete a miles de apostadores que confían en nuestros pronósticos expertos y análisis detallados para maximizar sus ganancias.'
        : 'Join thousands of bettors who trust our expert predictions and detailed analysis to maximize their winnings.',
      type: 'website',
      locale: locale,
      siteName: 'BetTeam'
    },
    twitter: {
      card: 'summary_large_image',
      title: isSpanish
        ? 'BetTeam - Tu Equipo Para Apostar Con Confianza'
        : 'BetTeam - Your Team To Bet With Confidence',
      description: isSpanish
        ? 'Únete a miles de apostadores que confían en nuestros pronósticos expertos y análisis detallados para maximizar sus ganancias.'
        : 'Join thousands of bettors who trust our expert predictions and detailed analysis to maximize their winnings.'
    },
    alternates: {
      languages: {
        'es-MX': '/es-MX',
        'en': '/en',
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // Await the params
  const { locale } = await params;

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <head>
        {/* DNS预取优化 - Google Font组件已自动处理preconnect */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
        
        {/* 视口优化 */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        
        {/* 性能优化 */}
        <meta name="theme-color" content="#111114" />
        <meta name="color-scheme" content="dark" />
        
        {/* 可访问性改进 */}
        <meta name="format-detection" content="telephone=no" />
        
        {/* 预加载关键资源 */}
        <link rel="preload" href="/hero/hero_bg.png" as="image" />
      </head>
      <body className={`${montserrat.className} antialiased`}>
        {/* 跳转到主内容的链接 - 可访问性改进 */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {locale === 'es-MX' ? 'Saltar al contenido principal' : 'Skip to main content'}
        </a>
        
        <NextIntlClientProvider messages={messages}>
          <main id="main-content">
            {children}
          </main>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
