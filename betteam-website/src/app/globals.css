@import "tailwindcss";

:root {
  --background: #111114;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

/* 统一的背景颜色类 - 方便后续调整 */
.bg-primary {
  background-color: var(--background);
}

/* Stats Section 样式 */
.stats-card {
  width: 100%; /* 自适应宽度 */
  min-height: 224.99px; /* 最小高度保持一致 */
  aspect-ratio: 4/3; /* 保持宽高比 */
  border-radius: 24px;
  background-color: #0051FF1A;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.stats-value {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 50.56px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: #ffffff;
  margin-bottom: 8px;
}

.stats-label {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 25.28px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: #687496;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom animations */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

/* Utility classes */
.min-touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Header styles */
.header-nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.header-nav-link:hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: #3b82f6;
  border-radius: 1px;
}

/* Smooth scroll behavior for anchor links */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed header */
}

/* 滚动动画的初始状态 */
.opacity-0 {
  opacity: 0;
}

/* 确保动画元素在加载时不可见 */
[class*="animate-"]:not(.animate-scroll) {
  animation-fill-mode: both;
  opacity: 0;
}

/* 动画开始时显示元素 */
[class*="animate-"]:not(.animate-scroll).animate-fade-in,
[class*="animate-"]:not(.animate-scroll).animate-slide-up,
[class*="animate-"]:not(.animate-scroll).animate-slide-down,
[class*="animate-"]:not(.animate-scroll).animate-slide-in-left,
[class*="animate-"]:not(.animate-scroll).animate-slide-in-right,
[class*="animate-"]:not(.animate-scroll).animate-scale-in,
[class*="animate-"]:not(.animate-scroll).animate-bounce-in,
[class*="animate-"]:not(.animate-scroll).animate-fade-slide-up,
[class*="animate-"]:not(.animate-scroll).animate-fade-slide-down,
[class*="animate-"]:not(.animate-scroll).animate-rotate-in,
[class*="animate-"]:not(.animate-scroll).animate-flip-in,
[class*="animate-"]:not(.animate-scroll).animate-zoom-in,
[class*="animate-"]:not(.animate-scroll).animate-elastic-in {
  opacity: 1;
}

/* 改善动画性能 */
[class*="animate-"] {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* 减少动画在移动设备上的复杂度 */
@media (prefers-reduced-motion: reduce) {
  [class*="animate-"] {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 为交错动画提供更好的性能 */
.stagger-container > * {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 增强动画可见性 */
@keyframes enhancedFadeIn {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 为移动端优化动画 */
@media (max-width: 768px) {
  [class*="animate-"] {
    animation-duration: 0.8s !important;
  }

  /* 移动端滚动优化 */
  html {
    scroll-padding-top: 100px; /* 增加移动端的滚动填充 */
  }

  /* 确保移动端动画更容易触发 */
  .opacity-0 {
    opacity: 0;
    transform: translateY(20px); /* 减少移动端的初始偏移 */
  }

  /* 移动端减少动画的初始变换距离 */
  @keyframes slideUp {
    0% { transform: translateY(40px); opacity: 0; } /* 移动端减少距离 */
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes fadeSlideUp {
    0% { transform: translateY(30px); opacity: 0; } /* 移动端减少距离 */
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInLeft {
    0% { transform: translateX(-100px); opacity: 0; } /* 移动端减少距离 */
    100% { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInRight {
    0% { transform: translateX(100px); opacity: 0; } /* 移动端减少距离 */
    100% { transform: translateX(0); opacity: 1; }
  }
}
