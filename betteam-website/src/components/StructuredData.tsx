interface StructuredDataProps {
  locale: string;
}

export default function StructuredData({ locale }: StructuredDataProps) {
  const isSpanish = locale === 'es-MX';
  
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "BetTeam",
    "description": isSpanish 
      ? "Líder en pronósticos deportivos con análisis expertos y estadísticas avanzadas"
      : "Leader in sports predictions with expert analysis and advanced statistics",
    "url": `https://betteam.com/${locale}`,
    "logo": "https://betteam.com/logo.png",
    "sameAs": [
      "https://twitter.com/betteam",
      "https://facebook.com/betteam",
      "https://instagram.com/betteam"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "availableLanguage": ["Spanish", "English"]
    }
  };

  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "BetTeam",
    "url": `https://betteam.com/${locale}`,
    "description": isSpanish
      ? "Tu equipo para apostar con confianza. Pronósticos expertos y análisis detallados."
      : "Your team to bet with confidence. Expert predictions and detailed analysis.",
    "inLanguage": locale,
    "potentialAction": {
      "@type": "SearchAction",
      "target": `https://betteam.com/${locale}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string"
    }
  };

  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": isSpanish ? "Pronósticos Deportivos" : "Sports Predictions",
    "description": isSpanish
      ? "Servicio profesional de pronósticos deportivos con análisis experto y estadísticas avanzadas"
      : "Professional sports prediction service with expert analysis and advanced statistics",
    "provider": {
      "@type": "Organization",
      "name": "BetTeam"
    },
    "serviceType": isSpanish ? "Análisis Deportivo" : "Sports Analysis",
    "areaServed": "Worldwide"
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceData)
        }}
      />
    </>
  );
}
