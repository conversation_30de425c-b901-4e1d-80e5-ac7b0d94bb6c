'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';

// 证书数据接口
interface CertificateItem {
  id: string;
  image: string;
}

interface CertificateCarouselProps {
  items: CertificateItem[];
}

export default function CertificateCarousel({ items }: CertificateCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  // 响应式每页显示数量 - 优化为更大的图片显示
  const [itemsPerView, setItemsPerView] = useState(1);

  useEffect(() => {
    const updateItemsPerView = () => {
      const width = window.innerWidth;
      const totalItems = items.length;
      
      // 如果只有一张证书，始终显示1张
      if (totalItems === 1) {
        setItemsPerView(1);
        return;
      }
      
      // 根据屏幕尺寸和证书数量智能调整显示数量
      if (width >= 1280) { // xl - 大屏幕最多显示2张，让图片更大
        setItemsPerView(Math.min(2, totalItems));
      } else if (width >= 768) { // md - 中等屏幕显示1-2张
        setItemsPerView(Math.min(2, totalItems));
      } else { // sm - 小屏幕只显示1张
        setItemsPerView(1);
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, [items.length]);

  // 计算最大索引，确保不会超出范围
  const maxIndex = Math.max(0, items.length - itemsPerView);
  
  // 检查是否需要显示导航按钮
  const shouldShowNavigation = items.length > itemsPerView;

  const goToNext = () => {
    setCurrentIndex(prev => prev >= maxIndex ? 0 : prev + 1);
  };

  const goToPrev = () => {
    setCurrentIndex(prev => prev <= 0 ? maxIndex : prev - 1);
  };

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNext();
    }
    if (isRightSwipe) {
      goToPrev();
    }
  };

  // 渲染证书项 - 优化移动端显示
  const renderCertificate = (item: CertificateItem) => {
    if (item.image) {
      return (
        <figure 
          className="w-full flex items-center justify-center bg-white/5 rounded-lg"
          style={{
            height: '280px', // 移动端固定高度
            padding: '16px',
            minHeight: '280px' // 确保最小高度
          }}
          role="img"
          aria-labelledby={`certificate-${item.id}-caption`}
        >
          <Image 
            src={item.image} 
            alt={`BETTEAM certificate ${item.id}`} 
            width={400}
            height={280}
            className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
            style={{
              width: 'auto',
              height: 'auto',
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          />
          <figcaption 
            id={`certificate-${item.id}-caption`}
            className="sr-only"
          >
            BETTEAM certificate {item.id}
          </figcaption>
        </figure>
      );
    }
    
    // 占位符显示 - 移动端优化
    return (
      <div 
        className="w-full bg-gray-100 rounded-lg flex flex-col items-center justify-center"
        style={{
          height: '280px',
          padding: '24px',
          minHeight: '280px'
        }}
        role="img"
        aria-label={`certificate ${item.id}`}
      >
        <div className="text-gray-400 text-4xl mb-4" aria-hidden="true">📜</div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Certificate {item.id}</h3>
        <p className="text-gray-500 text-center text-sm">Certificate placeholder</p>
      </div>
    );
  };

  return (
    <div 
      className="relative"
      role="region"
      aria-label="certificate carousel"
      aria-live="polite"
    >
      {/* 导航箭头 - 只在需要切换时显示 */}
      {shouldShowNavigation && (
        <>
          <button
            onClick={goToPrev}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="go to previous certificate"
            type="button"
          >
            <ChevronLeft className="w-6 h-6 text-white" aria-hidden="true" />
          </button>
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="go to next certificate"
            type="button"
          >
            <ChevronRight className="w-6 h-6 text-white" aria-hidden="true" />
          </button>
        </>
      )}

      {/* 轮播容器 - 优化布局 */}
      <div 
        ref={carouselRef}
        className="overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        role="group"
        aria-label={`certificate ${currentIndex + 1} to ${Math.min(currentIndex + itemsPerView, items.length)} of ${items.length}`}
        style={{
          paddingLeft: shouldShowNavigation ? '60px' : '0',
          paddingRight: shouldShowNavigation ? '60px' : '0'
        }}
      >
        {items.length === 1 ? (
          <div className="flex justify-center">
            <div 
              className="w-full"
              style={{
                maxWidth: '400px'
              }}
            >
              {renderCertificate(items[0])}
            </div>
          </div>
        ) : (
          <div 
            className="flex transition-transform duration-300 ease-in-out"
            style={{
              transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
              gap: '24px' // 使用固定间距而不是 Tailwind 类
            }}
          >
            {items.map((item) => (
              <div 
                key={item.id} 
                className="flex-shrink-0"
                style={{ 
                  width: `calc((100% - ${(itemsPerView - 1) * 24}px) / ${itemsPerView})` 
                }}
              >
                {renderCertificate(item)}
              </div>
            ))}
          </div>
        )}
      </div>


    </div>
  );
}