'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';

interface TickerItem {
  id: string;
  label: string;
  text: string;
}

export default function Ticker() {
  const t = useTranslations('ticker');

  const tickerData: TickerItem[] = t.raw('items');
  const [isPaused, setIsPaused] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const tickerRef = useRef<HTMLDivElement>(null);

  const duplicatedData = [...tickerData, ...tickerData];

  // 检测是否为移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 根据设备类型设置动画持续时间
  const getAnimationDuration = () => {
    return '10s'; // 桌面 - 1分钟
  };

  return (
    <section className="py-4">
      <div className="container mx-auto px-4">
        {/* 跑马灯容器 - 水平滚动 */}
        <div className="overflow-hidden">
          <div
            ref={tickerRef}
            className="flex"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
            style={{
              animationName: 'scroll',
              animationDuration: isMobile ? getAnimationDuration() : '60s',
              animationTimingFunction: 'linear',
              animationIterationCount: 'infinite',
              animationPlayState: isPaused ? 'paused' : 'running',
              willChange: 'transform'
            }}
          >
            {duplicatedData.map((item, index) => (
              <div
                key={`${item.id}-${index}`}
                className="flex items-center justify-between px-4 py-4 rounded-[10px] flex-shrink-0"
                style={{
                  backgroundColor: '#0F172B',
                  minWidth: '320px', // 固定最小宽度确保内容显示完整
                  marginRight: '20px' // 卡片间距20px
                }}
              >
                {/* Label部分 - 蓝色背景 */}
                <span className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold flex-shrink-0">
                  {item.label}
                </span>
                
                {/* Text部分 - 白色文字 */}
                <span className="text-white text-sm font-medium ml-3 flex-1 text-right leading-tight">
                  {item.text}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
