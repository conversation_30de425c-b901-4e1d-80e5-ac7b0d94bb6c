'use client';

import { useEffect, useRef, useState } from 'react';

// 动画类型定义
export type AnimationType = 
  | 'fade-in'
  | 'slide-up'
  | 'slide-down'
  | 'slide-in-left'
  | 'slide-in-right'
  | 'scale-in'
  | 'bounce-in'
  | 'fade-slide-up'
  | 'fade-slide-down'
  | 'rotate-in'
  | 'flip-in'
  | 'zoom-in'
  | 'elastic-in';

interface ScrollAnimationWrapperProps {
  children: React.ReactNode;
  animation?: AnimationType;
  delay?: number; // 延迟时间（毫秒）
  duration?: number; // 动画持续时间（毫秒）
  threshold?: number; // 触发阈值 (0-1)
  rootMargin?: string; // 根边距
  className?: string;
  once?: boolean; // 是否只触发一次
  stagger?: boolean; // 是否启用交错动画（用于子元素）
  staggerDelay?: number; // 交错延迟时间（毫秒）
}

export default function ScrollAnimationWrapper({
  children,
  animation = 'fade-slide-up',
  delay = 0,
  duration = 1000,
  threshold = 0.2,
  rootMargin = '100px',
  className = '',
  once = true,
  stagger = false,
  staggerDelay = 200
}: ScrollAnimationWrapperProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 移动端使用更合理的触发条件
    const mobileThreshold = Math.min(threshold, 0.08); // 移动端最多8%，保持视觉效果
    const mobileRootMargin = isMobile ? '120px' : rootMargin; // 移动端适度增加根边距

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          if (!hasAnimated || !once) {
            // 移动端添加小延迟，让用户有时间注意到元素
            const delay = isMobile ? 100 : 0;
            setTimeout(() => {
              setIsVisible(true);
              if (once) {
                setHasAnimated(true);
              }
            }, delay);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        threshold: isMobile ? mobileThreshold : threshold,
        rootMargin: mobileRootMargin
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin, once, hasAnimated, isMobile]);

  // 处理交错动画
  useEffect(() => {
    if (!isVisible || !stagger || !elementRef.current) return;

    const childElements = elementRef.current.children;
    Array.from(childElements).forEach((child, index) => {
      const htmlChild = child as HTMLElement;
      htmlChild.style.animationDelay = `${delay + (index * staggerDelay)}ms`;
    });
  }, [isVisible, stagger, delay, staggerDelay]);

  // 构建动画类名
  const getAnimationClass = () => {
    if (!isVisible) return 'opacity-0 transform';

    const baseClass = `animate-${animation}`;
    return `${baseClass} opacity-100`;
  };

  // 构建样式
  const getAnimationStyle = () => {
    const style: React.CSSProperties = {
      animationDuration: `${duration}ms`,
      animationFillMode: 'both',
    };

    if (!stagger) {
      style.animationDelay = `${delay}ms`;
    }

    return style;
  };

  return (
    <div
      ref={elementRef}
      className={`${getAnimationClass()} ${className}`}
      style={getAnimationStyle()}
    >
      {children}
    </div>
  );
}

// 预设动画配置的便捷组件
export function FadeInSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="fade-in"
      delay={delay}
      duration={1200}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function SlideUpSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="fade-slide-up"
      delay={delay}
      duration={1000}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function SlideInLeftSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="slide-in-left"
      delay={delay}
      duration={1200}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function SlideInRightSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="slide-in-right"
      delay={delay}
      duration={1200}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function ZoomInSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="zoom-in"
      delay={delay}
      duration={1000}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function BounceInSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="bounce-in"
      delay={delay}
      duration={1500}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}

export function StaggeredSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}) {
  return (
    <ScrollAnimationWrapper
      animation="fade-slide-up"
      delay={delay}
      duration={600}
      stagger={true}
      staggerDelay={150}
      className={className}
    >
      {children}
    </ScrollAnimationWrapper>
  );
}
