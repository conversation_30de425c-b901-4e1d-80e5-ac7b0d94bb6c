'use client';

import { useState, useRef } from 'react';
import { ChevronDown } from 'lucide-react';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

interface AnimatedFAQProps {
  items: FAQItem[];
  className?: string;
}

export default function AnimatedFAQ({ items, className = '' }: AnimatedFAQProps) {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());
  const contentRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

  const toggleItem = (id: number) => {
    setOpenItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const isOpen = (id: number) => openItems.has(id);

  return (
    <div className={`space-y-4 ${className}`}>
      {items.map((item, index) => (
        <div
          key={item.id}
          className="group overflow-hidden"
          style={{
            background: 'linear-gradient(90deg, rgba(0, 81, 255, 0.2) 0%, rgba(0, 48, 153, 0.2) 100%)',
            borderRadius: '10px',
            animationDelay: `${index * 100}ms`
          }}
        >
          {/* 问题按钮 */}
          <button
            onClick={() => toggleItem(item.id)}
            className="w-full p-6 cursor-pointer hover:bg-black/10 transition-all duration-300 flex items-center justify-between min-h-[60px]"
            style={{
              fontFamily: 'Montserrat',
              fontWeight: 700,
              fontSize: '20px',
              lineHeight: '100%',
              letterSpacing: '0%',
              textAlign: 'left',
              color: '#FFFFFF'
            }}
            aria-expanded={isOpen(item.id)}
            aria-controls={`faq-content-${item.id}`}
          >
            <span className="flex-1 text-left pr-4">
              {item.question}
            </span>
            <ChevronDown 
              className={`w-6 h-6 text-white transition-all duration-300 flex-shrink-0 ${
                isOpen(item.id) ? 'rotate-180 scale-110' : 'rotate-0 scale-100'
              }`}
            />
          </button>

          {/* 答案内容 */}
          <div
            id={`faq-content-${item.id}`}
            ref={el => { contentRefs.current[item.id] = el; }}
            className={`overflow-hidden transition-all duration-500 ease-in-out ${
              isOpen(item.id) ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              maxHeight: isOpen(item.id) ? '500px' : '0px',
              transform: isOpen(item.id) ? 'translateY(0)' : 'translateY(-10px)'
            }}
          >
            <div
              className="px-6 pb-6 pt-2"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 500,
                fontSize: '16px',
                lineHeight: '140%',
                letterSpacing: '0%',
                color: '#687496'
              }}
            >
              <div
                className={`transition-all duration-300 ${
                  isOpen(item.id) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
                }`}
                style={{
                  transitionDelay: isOpen(item.id) ? '200ms' : '0ms'
                }}
              >
                {item.answer}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// 简化版本，用于现有的翻译系统
export function SimpleFAQSection({
  question,
  answer,
  index
}: {
  question: string;
  answer: string;
  index: number;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      className="group overflow-hidden animate-fade-slide-up"
      style={{
        background: 'linear-gradient(90deg, rgba(0, 81, 255, 0.2) 0%, rgba(0, 48, 153, 0.2) 100%)',
        borderRadius: '10px',
        animationDelay: `${index * 150}ms`,
        animationFillMode: 'both'
      }}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full p-6 cursor-pointer hover:bg-black/10 transition-all duration-300 flex items-center justify-between min-h-[60px]"
        style={{
          fontFamily: 'Montserrat',
          fontWeight: 700,
          fontSize: '20px',
          lineHeight: '100%',
          letterSpacing: '0%',
          textAlign: 'left',
          color: '#FFFFFF'
        }}
        aria-expanded={isOpen}
      >
        <span className="flex-1 text-left pr-4">
          {question}
        </span>
        <ChevronDown
          className={`w-6 h-6 text-white transition-all duration-300 flex-shrink-0 ${
            isOpen ? 'rotate-180 scale-110' : 'rotate-0 scale-100'
          }`}
        />
      </button>

      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          isOpen ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          maxHeight: isOpen ? '500px' : '0px',
          transform: isOpen ? 'translateY(0)' : 'translateY(-10px)'
        }}
      >
        <div
          className="px-6 pb-6"
          style={{
            fontFamily: 'Montserrat',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '140%',
            letterSpacing: '0%',
            color: '#687496',
            paddingTop: '1.5rem'
          }}
        >
          <div
            className={`transition-all duration-300 ${
              isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
            }`}
            style={{
              transitionDelay: isOpen ? '200ms' : '0ms'
            }}
          >
            {answer}
          </div>
        </div>
      </div>
    </div>
  );
}
