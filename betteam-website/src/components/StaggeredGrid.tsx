'use client';

import { useEffect, useRef, useState } from 'react';

interface StaggeredGridProps {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  animation?: 'fade-up' | 'fade-in' | 'scale-in' | 'slide-up' | 'zoom-in' | 'bounce-in';
  staggerDelay?: number; // 每个项目之间的延迟（毫秒）
  duration?: number; // 动画持续时间（毫秒）
  threshold?: number; // 触发阈值
  rootMargin?: string;
}

export default function StaggeredGrid({
  children,
  className = '',
  itemClassName = '',
  animation = 'fade-up',
  staggerDelay = 200,
  duration = 1000,
  threshold = 0.2,
  rootMargin = '100px'
}: StaggeredGridProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 移动端使用更宽松的触发条件
    const mobileThreshold = Math.min(threshold, 0.05); // 移动端最多5%
    const mobileRootMargin = isMobile ? '250px' : rootMargin; // 移动端增加根边距

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      {
        threshold: isMobile ? mobileThreshold : threshold,
        rootMargin: mobileRootMargin
      }
    );

    observer.observe(container);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin, hasAnimated, isMobile]);

  useEffect(() => {
    if (!isVisible || !containerRef.current) return;

    const items = containerRef.current.children;
    
    Array.from(items).forEach((item, index) => {
      const htmlItem = item as HTMLElement;
      
      // 设置初始状态
      htmlItem.style.opacity = '0';
      htmlItem.style.transform = getInitialTransform(animation);
      htmlItem.style.transition = `all ${duration}ms ease-out`;
      
      // 延迟应用动画
      setTimeout(() => {
        htmlItem.style.opacity = '1';
        htmlItem.style.transform = 'translateY(0) translateX(0) scale(1) rotate(0deg)';
      }, index * staggerDelay);
    });
  }, [isVisible, animation, staggerDelay, duration]);

  const getInitialTransform = (animationType: string): string => {
    switch (animationType) {
      case 'fade-up':
        return 'translateY(80px)';
      case 'fade-in':
        return 'translateY(0)';
      case 'scale-in':
        return 'scale(0.3)';
      case 'slide-up':
        return 'translateY(100px)';
      case 'zoom-in':
        return 'scale(0.1)';
      case 'bounce-in':
        return 'scale(0.1)';
      default:
        return 'translateY(80px)';
    }
  };

  return (
    <div
      ref={containerRef}
      className={className}
    >
      {children}
    </div>
  );
}

// 专门用于统计卡片的组件
export function StaggeredStatsGrid({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <StaggeredGrid
      animation="scale-in"
      staggerDelay={300}
      duration={1200}
      className={className}
    >
      {children}
    </StaggeredGrid>
  );
}

// 专门用于平台卡片的组件
export function StaggeredPlatformGrid({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <StaggeredGrid
      animation="fade-up"
      staggerDelay={250}
      duration={1000}
      threshold={0.05} // 降低阈值，只需5%进入视口就触发
      rootMargin="200px" // 增加根边距，提前200px触发动画
      className={className}
    >
      {children}
    </StaggeredGrid>
  );
}

// 专门用于支付渠道的组件
export function StaggeredPaymentGrid({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <StaggeredGrid
      animation="zoom-in"
      staggerDelay={150}
      duration={1000}
      className={className}
    >
      {children}
    </StaggeredGrid>
  );
}

// 专门用于FAQ项目的组件
export function StaggeredFAQList({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <StaggeredGrid
      animation="slide-up"
      staggerDelay={200}
      duration={800}
      className={className}
    >
      {children}
    </StaggeredGrid>
  );
}
