'use client';

import { useTranslations } from 'next-intl';

export default function Footer() {
  // 使用多语言 hook
  const t = useTranslations('footer');

  return (
    <div>
      {/* 分割线 */}
      <div className="container mx-auto px-6">
        <div
          className="w-1/2 h-0.5 mx-auto"
          style={{
            border: '1px solid',
            borderImageSource: 'linear-gradient(90deg, rgba(26, 39, 219, 0) 0%, #1A27DB 40.38%, #1A27DB 60%, rgba(26, 39, 219, 0) 100%)',
            borderImageSlice: 1
          }}
        />
      </div>
      <footer className="bg-primary pt-8 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {/* 应用下载部分 */}
          <div className="w-full max-w-md">
            <h3 
              className="text-white mb-4"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 700,
                fontSize: '24px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
            >
              {t('downloadApp')}
            </h3>
            <div className="flex flex-wrap gap-3">
              {/* App Store 按钮 */}
              <a 
                href="#" 
                className="flex items-center space-x-2.5"
              >
                <div className="flex items-center justify-center" style={{ width: '40px', height: '40px', backgroundColor: '#1A27DB', borderRadius: '10px', opacity: 1 }}>
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="white">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                </div>
                <div>
                  <div 
                    className="text-xs text-gray-400"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 400,
                      fontSize: '10px'
                    }}
                  >
                    {t('downloadButtons.appStoreSubtitle')}
                  </div>
                  <div 
                    className="text-white text-sm font-semibold"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 700,
                      fontSize: '16px'
                    }}
                  >
                    App Store
                  </div>
                </div>
              </a>

              {/* Google Play 按钮 */}
              <a 
                href="#" 
                className="flex items-center space-x-2.5"
              >
                <div className="flex items-center justify-center" style={{ width: '40px', height: '40px', backgroundColor: '#1A27DB', borderRadius: '10px', opacity: 1 }}>
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                    <path d="M3 20.5v-17c0-.5.4-.9.9-.9.2 0 .4.1.6.2l8.5 8.5-8.5 8.5c-.2.1-.4.2-.6.2-.5 0-.9-.4-.9-.9z" fill="#32BBFF"/>
                    <path d="M12 12l2.5-2.5 3.5 2-3.5 2L12 12z" fill="#32BBFF"/>
                    <path d="M12 12l-8.5-8.5L18 6.5 12 12z" fill="#00D277"/>
                    <path d="M12 12l6-3.5L21 12l-3 3.5L12 12z" fill="#FFB900"/>
                    <path d="M12 12l8.5 8.5L3.5 17.5 12 12z" fill="#F25022"/>
                  </svg>
                </div>
                <div>
                  <div 
                    className="text-xs text-gray-400"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 400,
                      fontSize: '10px'
                    }}
                  >
                    {t('downloadButtons.googlePlaySubtitle')}
                  </div>
                  <div 
                    className="text-white text-sm font-semibold"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 700,
                      fontSize: '16px'
                    }}
                  >
                    Google Play
                  </div>
                </div>
              </a>

              {/* AppGallery 按钮 */}
              <a 
                href="#" 
                className="flex items-center space-x-2.5"
              >
                <div className="flex items-center justify-center" style={{ width: '40px', height: '40px', backgroundColor: '#1A27DB', borderRadius: '10px', opacity: 1 }}>
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" fill="#FF6B6B"/>
                    <path d="M8 8h8v8H8z" fill="white"/>
                  </svg>
                </div>
                <div>
                  <div 
                    className="text-xs text-gray-400"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 400,
                      fontSize: '10px'
                    }}
                  >
                    {t('downloadButtons.appGallerySubtitle')}
                  </div>
                  <div 
                    className="text-white text-sm font-semibold"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 700,
                      fontSize: '16px'
                    }}
                  >
                    AppGallery
                  </div>
                </div>
              </a>
            </div>
          </div>
          
          {/* Sobre BetTeam MX 部分 */}
          <div>
            <h3 
              className="text-white mb-4"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 700,
                fontSize: '24px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
            >
              {t('aboutBetTeam')}
            </h3>
            <div className="space-y-5">
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('aboutLinks.platforms')}
              </a>
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('aboutLinks.community')}
              </a>
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('aboutLinks.certificates')}
              </a>
            </div>
          </div>
          
          {/* Soporte 部分 */}
          <div>
            <h3 
              className="text-white mb-4"
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 700,
                fontSize: '24px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
            >
              {t('support')}
            </h3>
            <div className="space-y-5">
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('supportLinks.faq')}
              </a>
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('supportLinks.telegram')}
              </a>
              <a 
                href="#" 
                className="block text-white"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {t('supportLinks.responsible')}
              </a>
            </div>
          </div>

        </div>
        
        {/* 社交媒体部分 - 独占一行 */}
        <div className="mt-8">
          <h3 
            className="text-white mb-4"
            style={{
              fontFamily: 'Montserrat',
              fontWeight: 700,
              fontSize: '24px',
              lineHeight: '100%',
              letterSpacing: '0%'
            }}
          >
            {t('followUs')}
          </h3>
          <div className="flex flex-wrap gap-3">
            {/* Facebook */}
            <a 
              href="#" 
              className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>

            {/* Instagram */}
            <a 
              href="#" 
              className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center hover:from-purple-600 hover:to-pink-600 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
              </svg>
            </a>

            {/* YouTube */}
            <a 
              href="#" 
              className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </a>

            {/* Twitter/X */}
            <a 
              href="#" 
              className="w-10 h-10 bg-black rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </a>

            {/* WhatsApp */}
            <a 
              href="#" 
              className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
              </svg>
            </a>

            {/* Telegram */}
            <a 
              href="#" 
              className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
              </svg>
            </a>
          </div>
        </div>
        
        {/* 版权信息 */}
        <div className="mt-8 pt-8 text-center">
          <p 
            style={{
              fontFamily: 'Montserrat',
              fontWeight: 500,
              fontSize: '16px',
              lineHeight: '100%',
              letterSpacing: '0%',
              color: '#687496'
            }}
          >
            {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
    </div>
  );
}