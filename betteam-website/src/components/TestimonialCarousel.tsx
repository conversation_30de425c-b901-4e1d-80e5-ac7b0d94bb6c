'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

// 推荐数据接口
interface TestimonialItem {
  id: string;
  name: string;
  rating: number;
  comment: string;
  position?: string;
}

interface TestimonialCarouselProps {
  items: TestimonialItem[];
}

export default function TestimonialCarousel({ items }: TestimonialCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  // 响应式每页显示数量
  const [itemsPerView, setItemsPerView] = useState(1);

  useEffect(() => {
    const updateItemsPerView = () => {
      const width = window.innerWidth;
      
      if (width >= 1280) { // xl
        setItemsPerView(3);
      } else if (width >= 1024) { // lg
        setItemsPerView(3);
      } else if (width >= 768) { // md
        setItemsPerView(2);
      } else { // sm
        setItemsPerView(1);
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  // 计算最大索引，确保不会超出范围
  const maxIndex = Math.max(0, items.length - itemsPerView);
  
  // 检查是否需要显示导航按钮
  const shouldShowNavigation = items.length > itemsPerView;

  const goToNext = () => {
    setCurrentIndex(prev => prev >= maxIndex ? 0 : prev + 1);
  };

  const goToPrev = () => {
    setCurrentIndex(prev => prev <= 0 ? maxIndex : prev - 1);
  };

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNext();
    }
    if (isRightSwipe) {
      goToPrev();
    }
  };

  // 渲染推荐项
  const renderTestimonial = (item: TestimonialItem) => (
    <article 
      className="p-6 h-full flex flex-col"
      style={{
        background: '#0051FF1A',
        borderRadius: '24px',
        minHeight: '180px'
      }}
      role="article"
      aria-labelledby={`testimonial-${item.id}-name`}
    >
      <header className="flex items-start mb-4">
        <div 
          className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-4 flex-shrink-0"
          role="img"
          aria-label={`${item.name}`}
        >
          <span className="text-white font-bold text-lg" aria-hidden="true">
            {item.name?.charAt(0) || 'U'}
          </span>
        </div>
        
        {/* 用户名和星级评分 */}
        <div className="flex-1 min-w-0">
          <h3 
            id={`testimonial-${item.id}-name`}
            className="text-white mb-2"
            style={{
              fontFamily: 'Montserrat',
              fontWeight: 700,
              fontSize: '16px',
              lineHeight: '100%',
              letterSpacing: '0%'
            }}
          >
            {item.name}
          </h3>
          <div 
            className="flex"
            role="img"
            aria-label={`${item.rating} out of 5 stars, ${item.name}'s testimonial`}
          >
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < item.rating 
                    ? 'text-yellow-400 fill-current' 
                    : 'fill-current'
                }`}
                style={{
                  color: i < item.rating ? '#FBBF24' : '#1A27DB'
                }}
                aria-hidden="true"
              />
            ))}
          </div>
        </div>
      </header>
      
      {/* 评价内容 */}
      <blockquote className="text-gray-300 text-sm leading-relaxed">
        &ldquo;{item.comment}&rdquo;
      </blockquote>
    </article>
  );

  return (
    <div 
      className="relative"
      role="region"
      aria-label="testimonial carousel"
      aria-live="polite"
    >
      {/* 导航箭头 - 只在需要切换时显示 */}
      {shouldShowNavigation && (
        <>
          <button
            onClick={goToPrev}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="go to previous testimonial"
            type="button"
          >
            <ChevronLeft className="w-6 h-6 text-white" aria-hidden="true" />
          </button>
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="go to next testimonial"
            type="button"
          >
            <ChevronRight className="w-6 h-6 text-white" aria-hidden="true" />
          </button>
        </>
      )}

      {/* 轮播容器 */}
      <div 
        ref={carouselRef}
        className="overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        role="group"
        aria-label={`testimonial ${currentIndex + 1} to ${Math.min(currentIndex + itemsPerView, items.length)} of ${items.length} testimonials`}
        style={{
          // 为导航按钮预留空间
          paddingLeft: shouldShowNavigation ? '60px' : '0',
          paddingRight: shouldShowNavigation ? '60px' : '0'
        }}
      >
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
            gap: '24px' // 使用固定间距而不是 Tailwind 类
          }}
        >
          {items.map((item) => (
            <div 
              key={item.id} 
              className="flex-shrink-0"
              style={{ 
                width: `calc((100% - ${(itemsPerView - 1) * 24}px) / ${itemsPerView})` 
              }}
            >
              {renderTestimonial(item)}
            </div>
          ))}
        </div>
      </div>


    </div>
  );
}