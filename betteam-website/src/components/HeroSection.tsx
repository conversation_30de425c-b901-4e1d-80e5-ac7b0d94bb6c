'use client';

interface HeroSectionProps {
  translations: {
    title: string;
    badge: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
}

export default function HeroSection({ translations }: HeroSectionProps) {
  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* 深色遮罩层，增强文字可读性 */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      {/* 头部背景图片叠加层 - 确保在大屏幕上完整显示 */}
      <div 
        className="absolute top-0 left-0 right-0 bg-no-repeat"
        style={{
          backgroundImage: "url('/hero/hero_bg_header.svg')",
          backgroundSize: 'contain', // 使用 contain 确保图片完整显示
          backgroundPosition: 'center top', // 从顶部开始显示
          height: '60vh', // 使用视口高度单位，在大屏幕上有更好的适应性
          minHeight: '400px' // 设置最小高度确保小屏幕上也有足够空间
        }}
      ></div> 

      
      {/* 内容容器 */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 min-h-screen">
        <div 
          className="text-center max-w-6xl mx-auto pb-24 sm:pb-32"
          style={{
            // 响应式顶部间距：小屏幕64px，中等屏幕动态调整，大屏幕600px
            paddingTop: 'clamp(64px, 22vw, 300px)'
          }}
        >
          
          {/* 副标题 */}
          <div className="mb-4 sm:mb-6">
            <span 
              className="inline-block text-white"
              style={{
                fontFamily: 'Montserrat, sans-serif',
                fontWeight: 500,
                fontSize: '34px',
                lineHeight: '100%',
                letterSpacing: '0%',
                textAlign: 'center'
              }}
            >
              {translations.badge}
            </span>
          </div>
          
          {/* 主标题 */}
          <h1 
            className="text-white mb-6 sm:mb-8 drop-shadow-2xl"
            style={{
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: 900,
              fontStyle: 'normal', // Black 对应 normal style + 900 weight
              fontSize: '50px',
              lineHeight: '100%',
              letterSpacing: '0%',
              textAlign: 'center'
            }}
          >
            <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
              {translations.title}
            </span>
          </h1>
          
          {/* 描述文字 */}
          <p 
            className="mb-10 sm:mb-12 max-w-4xl mx-auto drop-shadow-lg px-4"
            style={{
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: 500,
              fontStyle: 'normal', // Medium 对应 normal style + 500 weight
              fontSize: '26px',
              lineHeight: '100%',
              letterSpacing: '0%',
              textAlign: 'center',
              color: '#687496'
            }}
          >
            {translations.description}
          </p>
          
          {/* 行动按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4">
            <button 
              className="group relative w-full sm:w-auto text-white py-4 sm:py-5 px-8 sm:px-10 transition-all duration-300 min-h-[56px] shadow-xl hover:shadow-2xl transform hover:scale-105 overflow-hidden"
              style={{
                borderRadius: '12px',
                background: '#1A27DB',
                fontFamily: 'Montserrat, sans-serif',
                fontWeight: 700,
                fontStyle: 'normal', // Bold 对应 normal style + 700 weight
                fontSize: '28px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
            >
              <span className="relative z-10">{translations.cta.primary}</span>
              <div 
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: '#1520B8' // 悬停时稍微深一点的蓝色
                }}
              ></div>
            </button>
            <button 
              className="group relative w-full sm:w-auto text-white hover:bg-white hover:text-gray-900 py-4 sm:py-5 px-8 sm:px-10 transition-all duration-300 min-h-[56px] backdrop-blur-sm hover:backdrop-blur-none shadow-xl hover:shadow-2xl transform hover:scale-105 overflow-hidden"
              style={{
                borderRadius: '12px',
                border: '2px solid #1A27DB',
                fontFamily: 'Montserrat, sans-serif',
                fontWeight: 700,
                fontStyle: 'normal', // Bold 对应 normal style + 700 weight
                fontSize: '28px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
            >
              <span className="relative z-10">{translations.cta.secondary}</span>
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>
      </div>
      
      {/* 底部渐变过渡 */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-primary to-transparent"></div>
    </section>
  );
}