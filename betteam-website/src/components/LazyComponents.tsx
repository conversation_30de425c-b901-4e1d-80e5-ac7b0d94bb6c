'use client';

import dynamic from 'next/dynamic';
import { ComponentType, useState, useEffect, useRef } from 'react';

// 加载指示器组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-8 min-h-[200px]">
    <div className="flex flex-col items-center">
      <div className="w-6 h-6 md:w-8 md:h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      <span className="mt-2 text-sm text-gray-400 opacity-75">加载中...</span>
    </div>
  </div>
);

// 错误边界组件
const ErrorFallback = ({ retry }: { retry?: () => void }) => (
  <div className="flex flex-col items-center justify-center py-8 text-center">
    <div className="text-red-500 mb-4">
      <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2"></h3>
    <p className="text-gray-600 mb-4">fail</p>
    {retry && (
      <button
        onClick={retry}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        retry
      </button>
    )}
  </div>
);

// 动态导入的轮播组件
export const LazyTestimonialCarousel = dynamic(
  () => import('./TestimonialCarousel'),
  {
    loading: () => <LoadingSpinner />,
    ssr: false // 客户端渲染，减少初始包大小
  }
);

export const LazyCertificateCarousel = dynamic(
  () => import('./CertificateCarousel'),
  {
    loading: () => <LoadingSpinner />,
    ssr: false
  }
);

// 动态导入的其他大型组件
export const LazyListadoSection = dynamic(
  () => import('./ListadoSection'),
  {
    loading: () => <LoadingSpinner />,
    ssr: true // 保持服务端渲染以利于SEO
  }
);

export const LazyPayChannelSection = dynamic(
  () => import('./PayChannelSection'),
  {
    loading: () => <LoadingSpinner />,
    ssr: true
  }
);

export const LazyFooter = dynamic(
  () => import('./Footer'),
  {
    loading: () => <LoadingSpinner />,
    ssr: true
  }
);

// 高阶组件：添加错误边界
export function withErrorBoundary<T extends object>(
  Component: ComponentType<T>,
  fallback?: ComponentType<{ error: Error; retry: () => void }>
) {
  return function WrappedComponent(props: T) {
    // 简化的错误处理，实际项目中可以使用 react-error-boundary
    try {
      return <Component {...props} />;
    } catch (error) {
      const FallbackComponent = fallback || ErrorFallback;
      return (
        <FallbackComponent 
          error={error as Error} 
          retry={() => window.location.reload()} 
        />
      );
    }
  };
}

// 交集观察器Hook - 用于懒加载可见区域的组件
export function useIntersectionObserver(
  ref: React.RefObject<HTMLElement>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        // 一旦进入视口就设置为true，并且不再改变状态
        if (entry.isIntersecting) {
          setIsIntersecting(true);
        }
      },
      {
        threshold: 0.01, // 降低阈值，更容易触发
        rootMargin: '100px', // 增加根边距，提前加载
        ...options
      }
    );

    observer.observe(ref.current);

    return () => observer.disconnect();
  }, [ref, options]);

  return isIntersecting;
}

// 可见时才加载的组件包装器
export function LazyOnVisible({ 
  children, 
  fallback = <LoadingSpinner />,
  className = ''
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测是否为移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const isVisible = useIntersectionObserver(ref as React.RefObject<HTMLElement>, { 
    threshold: isMobile ? 0.01 : 0.1, // 移动端使用更低的阈值
    rootMargin: isMobile ? '150px' : '100px' // 移动端使用更大的根边距
  });

  // 添加超时机制和移动端特殊处理
  useEffect(() => {
    if (isVisible && !hasLoaded) {
      setHasLoaded(true);
    }
    
    // 移动端更短的超时时间，桌面端稍长
    const timeoutDuration = isMobile ? 3000 : 5000;
    const timeout = setTimeout(() => {
      if (!hasLoaded) {
        console.warn('LazyOnVisible: 强制加载组件 (超时保护)');
        setHasLoaded(true);
      }
    }, timeoutDuration);

    return () => clearTimeout(timeout);
  }, [isVisible, hasLoaded, isMobile]);

  // 添加滚动事件监听作为备用方案
  useEffect(() => {
    if (hasLoaded || !ref.current) return;

    const handleScroll = () => {
      if (!ref.current || hasLoaded) return;
      
      const rect = ref.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // 如果元素进入视口范围内（包括一定的缓冲区）
      if (rect.top < windowHeight + 200 && rect.bottom > -200) {
        setHasLoaded(true);
      }
    };

    // 立即检查一次
    handleScroll();
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasLoaded]);

  return (
    <div ref={ref} className={className}>
      {hasLoaded ? children : fallback}
    </div>
  );
}