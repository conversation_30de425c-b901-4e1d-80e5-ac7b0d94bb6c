'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import LanguageSwitcher from './LanguageSwitcher';
import MobileNavigation from './MobileNavigation';

interface HeaderProps {
  translations: {
    listado: string;
    comunidad: string;
    certificados: string;
    faq: string;
  };
}

export default function Header({ translations }: HeaderProps) {
  const [activeSection, setActiveSection] = useState('');

  const navItems = [
    { id: 'listado', href: '#listado', label: translations.listado },
    { id: 'comunidad', href: '#comunidad', label: translations.comunidad },
    { id: 'certificados', href: '#certificados', label: translations.certificados },
    { id: 'faq', href: '#faq', label: translations.faq }
  ];

  // Handle scroll to detect active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = ['listado', 'comunidad', 'certificados', 'faq'];
      const scrollPosition = window.scrollY + 100; // Offset for header height

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial position

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href: string, sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      // 将焦点移动到目标元素以改善可访问性
      const targetElement = element as HTMLElement;
      if (targetElement.tabIndex === -1) {
        targetElement.tabIndex = -1;
      }
      targetElement.focus();
    }
  };

  // 键盘导航处理
  const handleKeyDown = (event: React.KeyboardEvent, href: string, sectionId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleNavClick(href, sectionId);
    }
  };

  return (
    <>
      <header 
        className="fixed top-0 left-0 right-0 z-50 backdrop-blur-sm" 
        style={{ height: '80px', backgroundColor: '#10101080' }}
        role="banner"
        aria-label="primary navigation"
      >
        <div className="max-w-7xl mx-auto px-6 h-full flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <a 
              href="#main-content" 
              className="focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
              aria-label="BETTEAM Home"
            >
              <Image 
                src="/logo/logo.svg" 
                alt="BETTEAM Logo" 
                width={216} 
                height={50} 
                className="object-contain"
                style={{ width: '216px', height: '50px' }}
                priority
              />
            </a>
          </div>

          {/* Right side - Desktop Navigation + Language Switcher + Mobile Menu */}
          <div className="flex items-center space-x-6">
            {/* Desktop Navigation */}
            <nav 
              className="hidden md:flex items-center space-x-6" 
              role="navigation" 
              aria-label="primary navigation menu"
            >
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.href, item.id)}
                  onKeyDown={(e) => handleKeyDown(e, item.href, item.id)}
                  className={`header-nav-link px-4 py-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    activeSection === item.id
                      ? 'text-white rounded-lg'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg'
                  }`}
                  style={{
                    fontFamily: 'Montserrat',
                    fontWeight: activeSection === item.id ? 700 : 400,
                    fontSize: '16px',
                    lineHeight: '100%',
                    letterSpacing: '0%',
                    backgroundColor: activeSection === item.id ? '#1A27DB' : 'transparent',
                    borderRadius: '8px'
                  }}
                  aria-current={activeSection === item.id ? 'page' : undefined}
                  aria-label={`${item.label} section`}
                  type="button"
                >
                  {item.label}
                </button>
              ))}
            </nav>
            
            <LanguageSwitcher />
            <MobileNavigation
              navItems={navItems}
              activeSection={activeSection}
              onNavClick={handleNavClick}
            />
          </div>
        </div>
      </header>

      {/* Spacer for fixed header */}
      <div style={{ height: '80px' }} />
    </>
  );
}
