'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

const locales = [
  { code: 'es-MX', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇲🇽' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
];

export default function LanguageSwitcher() {
  const t = useTranslations('header');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const currentLocale = locales.find(l => l.code === locale) || locales[0];

  const switchLanguage = (newLocale: string) => {
    // Store language preference in localStorage
    localStorage.setItem('preferred-locale', newLocale);
    
    // Navigate to the new locale
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
    setIsOpen(false);
  };

  // Load preferred language from localStorage on mount
  useEffect(() => {
    const preferredLocale = localStorage.getItem('preferred-locale');
    if (preferredLocale && preferredLocale !== locale && locales.some(l => l.code === preferredLocale)) {
      const newPath = pathname.replace(`/${locale}`, `/${preferredLocale}`);
      router.push(newPath);
    }
  }, [locale, pathname, router]);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-700 focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors min-h-[44px] min-w-[44px]"
        style={{ 
          backgroundColor: '#10101080',
          fontFamily: 'Montserrat',
          fontWeight: 700,
          fontSize: '16px',
          lineHeight: '100%',
          letterSpacing: '0%'
        }}
        aria-label={`${t('language')}: ${currentLocale.name}`}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <span className="text-lg" aria-hidden="true">{currentLocale.flag}</span>
        <span 
          className="hidden sm:inline text-white"
          style={{
            fontFamily: 'Montserrat',
            fontWeight: 700,
            fontSize: '16px',
            lineHeight: '100%',
            letterSpacing: '0%'
          }}
        >
          {currentLocale.code.toUpperCase()}
        </span>
        <ChevronDown className={`w-4 h-4 text-white transition-transform ${isOpen ? 'rotate-180' : ''}`} aria-hidden="true" />
      </button>

      {isOpen && (
        <div
          className="absolute top-full right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 min-w-[160px] z-50"
          role="listbox"
          aria-label={t('language')}
        >
          {locales.map((loc) => (
            <button
              key={loc.code}
              onClick={() => switchLanguage(loc.code)}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors first:rounded-t-lg last:rounded-b-lg min-h-[44px] ${
                loc.code === locale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
              style={{
                fontFamily: 'Montserrat',
                fontWeight: 700,
                fontSize: '16px',
                lineHeight: '100%',
                letterSpacing: '0%'
              }}
              role="option"
              aria-selected={loc.code === locale}
            >
              <span className="text-lg" aria-hidden="true">{loc.flag}</span>
              <span 
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 700,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {loc.name}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
}
