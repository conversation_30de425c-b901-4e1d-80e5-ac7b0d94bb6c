'use client';

import React from 'react';

interface ActionButtonsProps {
  playNowText: string;
  telegramText: string;
  whatsappText: string;
  tinyUrl?: string;
  telegramLink?: string;
  whatsappLink?: string;
}

export default function ActionButtons({
  playNowText,
  telegramText,
  whatsappText,
  tinyUrl,
  telegramLink,
  whatsappLink
}: ActionButtonsProps) {
  return (
    <>
      {/* 主要行动按钮 */}
      <a
        href={tinyUrl ? `https://${tinyUrl}` : '#'}
        target="_blank"
        rel="noopener nofollow"
        className="block w-full text-white transition-all duration-300 mb-4 shadow-lg hover:shadow-xl text-center"
        style={{
          height: '62px',
          background: '#1A27DB',
          borderRadius: '6px',
          padding: '10px 16px', // 移动端优化内边距
          fontFamily: 'Montserrat',
          fontWeight: 700,
          fontSize: 'clamp(18px, 4vw, 26px)', // 响应式字体大小
          lineHeight: '100%',
          letterSpacing: '0%',
          border: 'none',
          cursor: 'pointer',
          minWidth: '0', // 确保按钮可以收缩
          overflow: 'hidden', // 防止内容溢出
          textOverflow: 'ellipsis', // 文本溢出时显示省略号
          whiteSpace: 'nowrap', // 防止文本换行
          textDecoration: 'none',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#0F1A9E'; // 更深的蓝色
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = '#1A27DB'; // 恢复原色
        }}
      >
        {playNowText}
      </a>

      {/* 平台按钮组 */}
      <div className="flex gap-2 sm:gap-3">
        <a
          href={telegramLink || '#'}
          target="_blank"
          rel="noopener nofollow"
          className="flex-1 transition-all duration-300 text-center"
          style={{
            border: '2px solid #1A27DB',
            borderRadius: '6px',
            padding: '12px 8px', // 移动端减少内边距
            fontFamily: 'Montserrat',
            fontWeight: 700,
            fontSize: 'clamp(14px, 3vw, 16px)', // 响应式字体大小
            lineHeight: '100%',
            letterSpacing: '0%',
            background: 'transparent',
            color: '#FFFFFF', // 设置字体颜色为白色
            cursor: 'pointer',
            minWidth: '0', // 确保按钮可以收缩
            overflow: 'hidden', // 防止内容溢出
            textOverflow: 'ellipsis', // 文本溢出时显示省略号
            whiteSpace: 'nowrap', // 防止文本换行
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#0F1A9E'; // 统一使用深蓝色背景
            e.currentTarget.style.borderColor = '#0F1A9E'; // 统一使用深蓝色边框
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent'; // 恢复透明背景
            e.currentTarget.style.borderColor = '#1A27DB'; // 恢复原边框色
          }}
        >
          {telegramText}
        </a>
        <a
          href={whatsappLink || '#'}
          target="_blank"
          rel="noopener nofollow"
          className="flex-1 transition-all duration-300 text-center"
          style={{
            border: '2px solid #1A27DB',
            borderRadius: '6px',
            padding: '12px 8px', // 移动端减少内边距
            fontFamily: 'Montserrat',
            fontWeight: 700,
            fontSize: 'clamp(14px, 3vw, 16px)', // 响应式字体大小
            lineHeight: '100%',
            letterSpacing: '0%',
            background: 'transparent',
            color: '#FFFFFF', // 设置字体颜色为白色
            cursor: 'pointer',
            minWidth: '0', // 确保按钮可以收缩
            overflow: 'hidden', // 防止内容溢出
            textOverflow: 'ellipsis', // 文本溢出时显示省略号
            whiteSpace: 'nowrap', // 防止文本换行
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#0F1A9E'; // 统一使用深蓝色背景
            e.currentTarget.style.borderColor = '#0F1A9E'; // 统一使用深蓝色边框
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent'; // 恢复透明背景
            e.currentTarget.style.borderColor = '#1A27DB'; // 恢复原边框色
          }}
        >
          {whatsappText}
        </a>
      </div>
    </>
  );
}