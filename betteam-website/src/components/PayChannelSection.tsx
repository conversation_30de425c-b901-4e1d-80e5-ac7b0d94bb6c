'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { StaggeredPaymentGrid } from './StaggeredGrid';

// 支付渠道数据配置
const paymentChannels = [
  {
    id: 'mastercard',
    name: 'Mastercard',
    logo: '/pay/Mastercard.png'
  },
  {
    id: 'visa',
    name: 'VISA',
    logo: '/pay/VISA.png'
  },
  {
    id: 'spei',
    name: 'SPEI',
    logo: '/pay/SPEI.svg'
  },
  {
    id: 'oxxo',
    name: 'OXXO',
    logo: '/pay/OXXO.svg'
  },
  {
    id: 'codi',
    name: 'CoDi',
    logo: '/pay/CoDi.svg'
  },
  {
    id: 'paypal',
    name: 'PayPal',
    logo: '/pay/PayPal.png'
  }
];

export default function PayChannelSection() {
  const t = useTranslations('paymentChannels');
  
  return (
    <section className="pt-8 pb-8 bg-primary">
      <div className="container mx-auto px-4">
        {/* 标题 - 与其他 section 保持一致的样式 */}
        <h2 
          className="text-white mb-16"
          style={{
            fontFamily: 'Montserrat',
            fontWeight: 900,
            fontStyle: 'normal',
            fontSize: '30px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textAlign: 'left'
          }}
        >
          {t('title')}
        </h2>

        {/* 支付渠道网格 */}
        <StaggeredPaymentGrid className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8">
          {paymentChannels.map((channel) => (
            <div
              key={channel.id}
              className="flex flex-col items-center justify-center p-6 rounded-lg hover:bg-black/10 transition-all duration-300"
              style={{
                background: 'linear-gradient(90deg, rgba(0, 81, 255, 0.2) 0%, rgba(0, 48, 153, 0.2) 100%)',
                borderRadius: '15px',
                height: '107px',
                width: '100%'
              }}
            >
              {/* 支付渠道图标 */}
              <div className="relative w-16 h-12 md:w-20 md:h-14 mb-3 flex items-center justify-center">
                <Image
                  src={channel.logo}
                  alt=""
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 64px, 80px"
                />
              </div>
              
              {/* 支付渠道名称 */}
              <span 
                className="text-white text-center"
                style={{
                  fontFamily: 'Montserrat',
                  fontWeight: 700,
                  fontSize: '16px',
                  lineHeight: '100%',
                  letterSpacing: '0%'
                }}
              >
                {channel.name}
              </span>
            </div>
          ))}
        </StaggeredPaymentGrid>
      </div>
    </section>
  );
}