'use client';

import { useState } from 'react';
import { Menu, X } from 'lucide-react';

interface NavItem {
  id: string;
  href: string;
  label: string;
}

interface MobileNavigationProps {
  navItems: NavItem[];
  activeSection?: string;
  onNavClick: (href: string, sectionId: string) => void;
}

export default function MobileNavigation({ navItems, activeSection, onNavClick }: MobileNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleNavClick = (href: string, sectionId: string) => {
    setIsMobileMenuOpen(false);
    onNavClick(href, sectionId);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="md:hidden p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label={isMobileMenuOpen ? "close" : "open"}
        aria-expanded={isMobileMenuOpen}
        aria-controls="mobile-navigation-menu"
        type="button"
      >
        {isMobileMenuOpen ? (
          <X className="w-6 h-6" aria-hidden="true" />
        ) : (
          <Menu className="w-6 h-6" aria-hidden="true" />
        )}
      </button>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div
          id="mobile-navigation-menu"
          className="fixed left-0 right-0 bg-gray-900 border-t border-gray-700 z-[9999] md:hidden"
          style={{ top: '80px' }}
          role="dialog"
          aria-modal="true"
          aria-label=""
        >
          <nav 
            className="px-6 py-4 space-y-2"
            role="navigation"
            aria-label="menu"
          >
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavClick(item.href, item.id)}
                className={`w-full text-left px-4 py-3 rounded-lg transition-colors min-h-[44px] flex items-center font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }`}
                aria-current={activeSection === item.id ? 'page' : undefined}
                aria-label={`go to ${item.label} section`}
                type="button"
              >
                {item.label}
              </button>
            ))}
          </nav>
        </div>
      )}
    </>
  );
}
