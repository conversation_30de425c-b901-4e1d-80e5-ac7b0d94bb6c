'use client';

import { useEffect, useRef, useState } from 'react';
import Footer from './Footer';

export default function AnimatedFooter() {
  const footerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            console.log('Footer is now visible - triggering animation');
          }
        });
      },
      {
        threshold: 0.2, // 当Footer的20%进入视口时触发
        rootMargin: '0px' // 不提前检测，让动画更明显
      }
    );

    if (footerRef.current) {
      observer.observe(footerRef.current);
    }

    return () => {
      if (footerRef.current) {
        observer.unobserve(footerRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={footerRef}
      className={`transition-all duration-[4000ms] ease-out ${
        isVisible
          ? 'opacity-100 transform translate-y-0 scale-100'
          : 'opacity-0 transform translate-y-20 scale-95'
      }`}
      style={{
        willChange: 'opacity, transform',
        backfaceVisibility: 'hidden',
        transitionTimingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      }}
    >
      <Footer />
    </div>
  );
}
