/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Montserrat', 'sans-serif'],
      },
      screens: {
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
      animation: {
        'scroll': 'scroll 60s linear infinite',
        'fade-in': 'fadeIn 1.2s ease-out',
        'slide-up': 'slideUp 1s ease-out',
        'slide-down': 'slideDown 1s ease-out',
        'slide-in-left': 'slideInLeft 1.2s ease-out',
        'slide-in-right': 'slideInRight 1.2s ease-out',
        'scale-in': 'scaleIn 1s ease-out',
        'bounce-in': 'bounceIn 1.5s ease-out',
        'fade-slide-up': 'fadeSlideUp 1s ease-out',
        'fade-slide-down': 'fadeSlideDown 1s ease-out',
        'rotate-in': 'rotateIn 1.2s ease-out',
        'flip-in': 'flipIn 1.2s ease-out',
        'zoom-in': 'zoomIn 1s ease-out',
        'elastic-in': 'elasticIn 1.5s ease-out',
        'accordion-open': 'accordionOpen 0.5s ease-out',
        'accordion-close': 'accordionClose 0.3s ease-in',
      },
      keyframes: {
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(80px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-80px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-200px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(200px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.1)', opacity: '0.7' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.1)', opacity: '0' },
          '25%': { transform: 'scale(1.2)', opacity: '0.6' },
          '50%': { transform: 'scale(0.8)', opacity: '0.8' },
          '75%': { transform: 'scale(1.1)', opacity: '0.9' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        fadeSlideUp: {
          '0%': { transform: 'translateY(60px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeSlideDown: {
          '0%': { transform: 'translateY(-60px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        rotateIn: {
          '0%': { transform: 'rotate(-360deg) scale(0.3)', opacity: '0' },
          '50%': { transform: 'rotate(-180deg) scale(0.8)', opacity: '0.5' },
          '100%': { transform: 'rotate(0deg) scale(1)', opacity: '1' },
        },
        flipIn: {
          '0%': { transform: 'perspective(400px) rotateY(-180deg)', opacity: '0' },
          '30%': { transform: 'perspective(400px) rotateY(-30deg)', opacity: '0.7' },
          '60%': { transform: 'perspective(400px) rotateY(20deg)', opacity: '0.9' },
          '100%': { transform: 'perspective(400px) rotateY(0deg)', opacity: '1' },
        },
        zoomIn: {
          '0%': { transform: 'scale(0.1)', opacity: '0' },
          '30%': { transform: 'scale(0.6)', opacity: '0.4' },
          '60%': { transform: 'scale(1.2)', opacity: '0.8' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        elasticIn: {
          '0%': { transform: 'scale(0.1)', opacity: '0' },
          '25%': { transform: 'scale(1.3)', opacity: '0.7' },
          '50%': { transform: 'scale(0.7)', opacity: '0.9' },
          '75%': { transform: 'scale(1.1)', opacity: '1' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        accordionOpen: {
          '0%': { maxHeight: '0', opacity: '0' },
          '100%': { maxHeight: '500px', opacity: '1' },
        },
        accordionClose: {
          '0%': { maxHeight: '500px', opacity: '1' },
          '100%': { maxHeight: '0', opacity: '0' },
        },
      },
      minHeight: {
        'touch': '44px',
      },
      minWidth: {
        'touch': '44px',
      },
    },
  },
  plugins: [],
}
