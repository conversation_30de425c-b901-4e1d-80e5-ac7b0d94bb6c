version: '3.8'

services:
  betteam-website:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    restart: unless-stopped
    networks:
      - betteam-network

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - betteam-website
    restart: unless-stopped
    networks:
      - betteam-network

networks:
  betteam-network:
    driver: bridge

volumes:
  node_modules: