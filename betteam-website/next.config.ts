import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  // Docker 部署配置
  output: 'standalone', // 启用 standalone 模式用于 Docker 部署
  
  // 性能优化配置
  compress: true, // 启用gzip压缩
  poweredByHeader: false, // 移除X-Powered-By头部
  
  // 图片优化配置
  images: {
    formats: ['image/webp', 'image/avif'], // 优先使用现代图片格式
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840], // 响应式图片尺寸
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384], // 图标尺寸
    minimumCacheTTL: 31536000, // 图片缓存1年
    dangerouslyAllowSVG: true, // 允许SVG
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // SVG安全策略
  },
  
  // 实验性功能
  experimental: {
    optimizePackageImports: ['lucide-react', 'framer-motion'], // 优化包导入
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  
  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production', // 生产环境移除console
  },
  
  // 头部优化
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/(.*)\\.(jpg|jpeg|png|webp|avif|ico|svg)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable', // 静态资源缓存1年
          },
        ],
      },
    ];
  },
};

export default withNextIntl(nextConfig);
