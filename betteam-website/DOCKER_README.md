# Docker 部署指南

## 文件说明

- `Dockerfile`: 用于构建 Next.js 应用的 Docker 镜像
- `docker-compose.yml`: 编排服务，包含应用和可选的 Nginx 反向代理
- `nginx.conf`: Nginx 配置文件
- `.dockerignore`: 指定构建时忽略的文件

## 快速开始

### 1. 仅运行应用

```bash
# 构建并启动应用
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

应用将在 http://localhost:3000 运行

### 2. 使用 Nginx 反向代理

如果需要使用 Nginx 作为反向代理（推荐生产环境），应用将在 http://localhost:80 运行

### 3. 仅构建 Docker 镜像

```bash
# 构建镜像
docker build -t betteam-website .

# 运行容器
docker run -p 3000:3000 betteam-website
```

## 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build --force-recreate

# 清理未使用的镜像
docker system prune -a
```

## 生产环境配置

### 环境变量

在 `docker-compose.yml` 中添加环境变量：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_PUBLIC_API_URL=https://your-api.com
  - DATABASE_URL=your-database-url
```

### HTTPS 配置

1. 将 SSL 证书放在 `ssl/` 目录下
2. 取消注释 `nginx.conf` 中的 HTTPS 配置
3. 重启服务

### 性能优化

- 使用多阶段构建减小镜像大小
- 启用 gzip 压缩
- 配置适当的缓存策略
- 使用 Alpine Linux 基础镜像

## 故障排除

### 常见问题

1. **Turbopack 构建错误**
   如果遇到 Turbopack 相关错误，使用简化版 Dockerfile：
   ```bash
   # 使用简化版 Dockerfile
   docker build -f Dockerfile.simple -t betteam-website .
   docker run -p 3000:3000 betteam-website
   ```

2. **端口冲突**
   ```bash
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "3001:3000"  # 使用 3001 端口
   ```

3. **构建失败**
   ```bash
   # 清理缓存重新构建
   docker-compose build --no-cache
   
   # 或者清理所有 Docker 缓存
   docker system prune -a
   ```

4. **权限问题**
   ```bash
   # 确保 Docker 有足够权限
   sudo docker-compose up
   ```

5. **Tailwind CSS 问题**
   项目使用 Tailwind CSS v4，如果遇到样式问题：
   - 确保使用 `npm run build:prod` 而不是带 turbopack 的构建
   - 检查 PostCSS 配置是否正确

### 查看详细日志

```bash
# 查看应用日志
docker-compose logs betteam-website

# 查看 Nginx 日志
docker-compose logs nginx
```

## 开发环境

对于开发环境，建议直接使用：

```bash
npm run dev
```

Docker 主要用于生产部署和测试生产构建。